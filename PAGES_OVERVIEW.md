# 📄 Oversikt: Sider og Filer i JobbLogg

## 🔗 URL-ruter og tilhørende filer:

### **Offentlige sider (ingen autentisering påkrevd)**
| URL | Fil | Beskrivelse |
|-----|-----|-------------|
| `/shared/:sharedId` | `src/pages/SharedProject/SharedProject.tsx` | Delt prosjektvisning for kunder |

### **Autentiseringssider**
| URL | Fil | Beskrivelse |
|-----|-----|-------------|
| `/sign-in` | `src/pages/SignIn/SignIn.tsx` | Innloggingsside |
| `/sign-up` | `src/pages/SignUp/SignUp.tsx` | Registreringsside |

### **Beskyttede sider (krever innlogging)**
| URL | Fil | Beskrivelse |
|-----|-----|-------------|
| `/` | `src/pages/Dashboard/Dashboard.tsx` | Hovedside/dashboard |
| `/archived-projects` | `src/pages/ArchivedProjects/ArchivedProjects.tsx` | Arkiverte prosjekter |
| `/conversations` | `src/pages/Conversations/Conversations.tsx` | Samtaler/meldinger |
| `/create` | `src/pages/CreateProject/CreateProjectWizard.tsx` | Opprett nytt prosjekt |
| `/create-wizard` | `src/pages/CreateProject/CreateProjectWizard.tsx` | Prosjektveiviser (samme som `/create`) |
| `/project/:projectId` | `src/pages/ProjectLog/ProjectLog.tsx` | Prosjektlogg (legg til nye oppføringer) |
| `/project/:projectId/details` | `src/pages/ProjectDetail/ProjectDetail.tsx` | Prosjektdetaljer/kontraktørpanel |

### **Utviklings-/testsider**
| URL | Fil | Beskrivelse |
|-----|-----|-------------|
| `/test-google-maps` | `src/pages/GoogleMapsTest.tsx` | Google Maps testing |

## 📁 Mappestruktur:

```
src/pages/
├── ArchivedProjects/
│   ├── ArchivedProjects.tsx     # Arkiverte prosjekter
│   └── index.ts                 # Export
├── Conversations/
│   └── Conversations.tsx        # Samtaler/chat oversikt
├── CreateProject/
│   ├── CreateProject.tsx        # Gammel opprett-side
│   ├── CreateProjectWizard.tsx  # Ny wizard-basert opprettelse
│   └── steps/                   # Wizard-steg komponenter
├── Dashboard/
│   └── Dashboard.tsx            # Hovedside med prosjektoversikt
├── ProjectDetail/
│   └── ProjectDetail.tsx        # Kontraktørpanel med prosjektdetaljer
├── ProjectLog/
│   └── ProjectLog.tsx           # Legg til nye loggoppføringer
├── SharedProject/
│   ├── SharedProject.tsx        # Kundevisning av delte prosjekter
│   └── index.ts                 # Export
├── SignIn/
│   └── SignIn.tsx               # Innlogging
├── SignUp/
│   └── SignUp.tsx               # Registrering
└── GoogleMapsTest.tsx           # Google Maps testing
```

## 🚀 Lazy Loading:

Alle sider bruker lazy loading gjennom `src/components/LazyComponents.tsx` for bedre ytelse:

- **LazyDashboard** → Dashboard.tsx
- **LazyCreateProject** → CreateProjectWizard.tsx  
- **LazyProjectDetail** → ProjectDetail.tsx
- **LazyProjectLog** → ProjectLog.tsx
- **LazySignIn** → SignIn.tsx
- **LazySignUp** → SignUp.tsx
- **LazySharedProject** → SharedProject.tsx
- **LazyConversations** → Conversations.tsx
- **LazyArchivedProjects** → ArchivedProjects.tsx
- **LazyGoogleMapsTest** → GoogleMapsTest.tsx

## 🔄 Navigasjonsflyt:

1. **Innlogging** → Dashboard
2. **Dashboard** → Opprett prosjekt / Se prosjektdetaljer
3. **Prosjektdetaljer** → Prosjektlogg / Arkiver
4. **Prosjektlogg** → Tilbake til detaljer/dashboard
5. **Del prosjekt** → Genererer link til SharedProject

## 📋 Hovedfunksjoner per side:

### Dashboard (`/`)
- Oversikt over alle aktive prosjekter
- "Nytt prosjekt" knapp
- Prosjektkort med handlinger
- Online status indikator

### Prosjektlogg (`/project/:projectId`)
- Legg til nye loggoppføringer med bilder
- Se tidligere logger
- Embedded chat per loggoppføring
- Like-funksjonalitet for kunder

### Prosjektdetaljer (`/project/:projectId/details`)
- Kontraktørpanel med full prosjektinformasjon
- Rediger prosjektdetaljer
- Arkiver/gjenåpne prosjekt
- Del prosjekt med kunder
- Statistikk og aktivitetslogg

### Opprett prosjekt (`/create`, `/create-wizard`)
- 3-stegs wizard for prosjektopprettelse
- Automatisk lagring av utkast
- Adressevalidering og Google Maps integrasjon
- Bildeopplasting

### Delt prosjekt (`/shared/:sharedId`)
- Kundevisning av prosjekt (read-only)
- Se loggoppføringer og bilder
- Chat med kontraktør
- Like bilder og oppføringer

### Arkiverte prosjekter (`/archived-projects`)
- Oversikt over arkiverte prosjekter
- Gjenåpne arkiverte prosjekter
- Søk og filtrer

### Samtaler (`/conversations`)
- Oversikt over alle samtaler
- Uleste meldinger
- Direkte navigasjon til chat

Denne strukturen gir en klar separasjon mellom ulike funksjonsområder og gjør det enkelt å vedlikeholde og utvide applikasjonen.
