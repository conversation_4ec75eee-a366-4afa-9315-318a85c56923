# 📄 Oversikt: Sider og Filer i JobbLogg

## 🔗 URL-ruter og tilhørende filer:

### **Offentlige sider (ingen autentisering påkrevd)**
| URL | Fil | Beskrivelse |
|-----|-----|-------------|
| `/shared/:sharedId` | `src/pages/SharedProject/SharedProject.tsx` | Delt prosjektvisning for kunder |

### **Autentiseringssider**
| URL | Fil | Beskrivelse |
|-----|-----|-------------|
| `/sign-in` | `src/pages/SignIn/SignIn.tsx` | Innloggingsside |
| `/sign-up` | `src/pages/SignUp/SignUp.tsx` | Registreringsside |

### **Beskyttede sider (krever innlogging)**
| URL | Fil | Beskrivelse |
|-----|-----|-------------|
| `/` | `src/pages/Dashboard/Dashboard.tsx` | Hovedside/dashboard |
| `/archived-projects` | `src/pages/ArchivedProjects/ArchivedProjects.tsx` | Arkiverte prosjekter |
| `/conversations` | `src/pages/Conversations/Conversations.tsx` | Samtaler/meldinger |
| `/create` | `src/pages/CreateProject/CreateProjectWizard.tsx` | Opprett nytt prosjekt |
| `/create-wizard` | `src/pages/CreateProject/CreateProjectWizard.tsx` | Prosjektveiviser (samme som `/create`) |
| `/project/:projectId` | `src/pages/ProjectLog/ProjectLog.tsx` | Prosjektlogg (legg til nye oppføringer) |
| `/project/:projectId/details` | `src/pages/ProjectDetail/ProjectDetail.tsx` | Prosjektdetaljer/kontraktørpanel |

### **Utviklings-/testsider**
| URL | Fil | Beskrivelse |
|-----|-----|-------------|
| `/test-google-maps` | `src/pages/GoogleMapsTest.tsx` | Google Maps testing |

## 📁 Mappestruktur:

```
src/pages/
├── ArchivedProjects/
│   ├── ArchivedProjects.tsx     # Arkiverte prosjekter
│   └── index.ts                 # Export
├── Conversations/
│   └── Conversations.tsx        # Samtaler/chat oversikt
├── CreateProject/
│   ├── CreateProject.tsx        # Gammel opprett-side
│   ├── CreateProjectWizard.tsx  # Ny wizard-basert opprettelse
│   └── steps/                   # Wizard-steg komponenter
├── Dashboard/
│   └── Dashboard.tsx            # Hovedside med prosjektoversikt
├── ProjectDetail/
│   └── ProjectDetail.tsx        # Kontraktørpanel med prosjektdetaljer
├── ProjectLog/
│   └── ProjectLog.tsx           # Legg til nye loggoppføringer
├── SharedProject/
│   ├── SharedProject.tsx        # Kundevisning av delte prosjekter
│   └── index.ts                 # Export
├── SignIn/
│   └── SignIn.tsx               # Innlogging
├── SignUp/
│   └── SignUp.tsx               # Registrering
└── GoogleMapsTest.tsx           # Google Maps testing
```

## 🚀 Lazy Loading:

Alle sider bruker lazy loading gjennom `src/components/LazyComponents.tsx` for bedre ytelse:

- **LazyDashboard** → Dashboard.tsx
- **LazyCreateProject** → CreateProjectWizard.tsx  
- **LazyProjectDetail** → ProjectDetail.tsx
- **LazyProjectLog** → ProjectLog.tsx
- **LazySignIn** → SignIn.tsx
- **LazySignUp** → SignUp.tsx
- **LazySharedProject** → SharedProject.tsx
- **LazyConversations** → Conversations.tsx
- **LazyArchivedProjects** → ArchivedProjects.tsx
- **LazyGoogleMapsTest** → GoogleMapsTest.tsx

## 🔄 Navigasjonsflyt:

1. **Innlogging** → Dashboard
2. **Dashboard** → Opprett prosjekt / Se prosjektdetaljer
3. **Prosjektdetaljer** → Prosjektlogg / Arkiver
4. **Prosjektlogg** → Tilbake til detaljer/dashboard
5. **Del prosjekt** → Genererer link til SharedProject

## 📋 Hovedfunksjoner per side:

### Dashboard (`/`)
- Oversikt over alle aktive prosjekter
- "Nytt prosjekt" knapp
- Prosjektkort med handlinger
- Online status indikator

### Prosjektlogg (`/project/:projectId`)
- Legg til nye loggoppføringer med bilder
- Se tidligere logger
- Embedded chat per loggoppføring
- Like-funksjonalitet for kunder

### Prosjektdetaljer (`/project/:projectId/details`)
- Kontraktørpanel med full prosjektinformasjon
- Rediger prosjektdetaljer
- Arkiver/gjenåpne prosjekt
- Del prosjekt med kunder
- Statistikk og aktivitetslogg

### Opprett prosjekt (`/create`, `/create-wizard`)
- 3-stegs wizard for prosjektopprettelse
- Automatisk lagring av utkast
- Adressevalidering og Google Maps integrasjon
- Bildeopplasting

### Delt prosjekt (`/shared/:sharedId`)
- Kundevisning av prosjekt (read-only)
- Se loggoppføringer og bilder
- Chat med kontraktør
- Like bilder og oppføringer

### Arkiverte prosjekter (`/archived-projects`)
- Oversikt over arkiverte prosjekter
- Gjenåpne arkiverte prosjekter
- Søk og filtrer

### Samtaler (`/conversations`)
- Oversikt over alle samtaler
- Uleste meldinger
- Direkte navigasjon til chat

## 🎨 Design System og Styling:

### **Hovedstiling og CSS**
| Fil | Beskrivelse |
|-----|-------------|
| `src/index.css` | Global CSS med Tailwind imports, custom properties og animasjoner |
| `src/App.css` | App-spesifikk styling (minimal bruk) |
| `tailwind.config.js` | Tailwind konfiguration med JobbLogg design tokens |

### **Design Tokens og Farger**
JobbLogg bruker et konsistent design system med prefiks `jobblogg-`:

**Hovedfarger:**
- `jobblogg-primary` (#2563EB) - Primær blå
- `jobblogg-accent` (#10B981) - Suksess grønn
- `jobblogg-warning` (#FBBF24) - Advarsel gul
- `jobblogg-error` (#DC2626) - Feil rød

**Bakgrunner:**
- `jobblogg-neutral` (#F8FAFC) - Kort bakgrunner
- `jobblogg-neutral-light` - Lysere variant
- `jobblogg-neutral-secondary` - Sekundær bakgrunn

**Tekst:**
- `jobblogg-text-strong` (#1F2937) - Sterk tekst
- `jobblogg-text-medium` (#4B5563) - Medium tekst
- `jobblogg-text-muted` (#9CA3AF) - Dempet tekst

### **UI Komponenter**
| Fil/Mappe | Beskrivelse |
|-----------|-------------|
| `src/components/ui/` | Gjenbrukbare UI komponenter |
| `src/components/ui/index.ts` | Barrel export for alle UI komponenter |
| `src/components/ui/Button/` | Knapp komponenter med varianter |
| `src/components/ui/Form/` | Skjema komponenter (TextArea, FormError, etc.) |
| `src/components/ui/Layout/` | Layout komponenter (PageLayout, etc.) |
| `src/components/ui/Typography/` | Tekst komponenter (TextStrong, TextMedium, etc.) |
| `src/components/ui/Modal/` | Modal og overlay komponenter |

## 📱 Mobilresponsivitet:

### **Breakpoints (Tailwind)**
```css
/* Standard Tailwind + JobbLogg custom */
xxs: 352px    /* Extra extra small */
xs: 378px     /* Extra small */
sm: 640px     /* Small */
md: 768px     /* Medium */
lg: 1024px    /* Large */
xl: 1280px    /* Extra large */
2xl: 1536px   /* 2X large */
```

### **Responsive Design Patterns**
JobbLogg følger mobile-first design med spesifikke mønstre:

**Wrapper Pattern:**
```jsx
<div className="
  w-full px-4        /* 100% bredde + padding på mobil */
  sm:px-0 sm:max-w-2xl sm:mx-auto  /* Sentrert med maks-bredde på desktop */
">
```

**Touch Targets:**
- Minimum 44x44px for alle interaktive elementer
- Større gap mellom elementer på mobil

**Responsive Komponenter:**
- `flex-wrap` for fleksible layouts
- `min-w-0` for å unngå overflow
- Conditional spacing: `gap-1 xs:gap-2`

### **Filer med Mobilresponsivitet**
| Fil | Responsive Features |
|-----|-------------------|
| `src/pages/Dashboard/Dashboard.tsx` | Responsive prosjektkort grid, mobile navigation |
| `src/pages/ProjectLog/ProjectLog.tsx` | Mobile-first loggkort, responsive bilder |
| `src/pages/CreateProject/CreateProjectWizard.tsx` | Mobile wizard steps, responsive forms |
| `src/components/ui/Layout/PageLayout.tsx` | Responsive container widths |
| `src/components/chat/` | Mobile-optimized chat bubbles og input |

### **Testing og Validering**
| Fil | Beskrivelse |
|-----|-------------|
| `tests/mobile-responsiveness.spec.ts` | Playwright tester for mobilresponsivitet |
| `src/utils/featureFlags.ts` | Feature flags for responsive features |

## 🔧 Konfigurasjoner:

### **Clerk (Autentisering)**
| Fil | Beskrivelse |
|-----|-------------|
| `src/styles/clerkAppearance.ts` | Custom styling for Clerk komponenter |
| `src/styles/clerkLocalization.ts` | Norsk oversettelse for Clerk |

### **Convex (Backend)**
| Fil | Beskrivelse |
|-----|-------------|
| `convex/schema.ts` | Database schema definisjon |
| `convex/_generated/` | Auto-genererte typer og API |

Denne strukturen gir en klar separasjon mellom ulike funksjonsområder og gjør det enkelt å vedlikeholde og utvide applikasjonen.
