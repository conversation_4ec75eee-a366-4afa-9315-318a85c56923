import React, { useState } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading2, Heading3, BodyText, TextMuted, PrimaryButton, ArchiveActions, ArchiveStatusBadge } from '../../components/ui';
import { ShareProjectModal } from '../../components/ShareProjectModal';





const ProjectDetail: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const { user } = useUser();
  const navigate = useNavigate();



  // Share modal state
  const [showShareModal, setShowShareModal] = useState(false);

  // Format date in short format
  const formatShortDate = (timestamp: number): string => {
    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) return 'Ugyldig dato';

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${day}.${month}.${year}`;
    } catch (error) {
      return 'Ugyldig dato';
    }
  };





  // Fetch project details
  const project = useQuery(
    api.projects.getById,
    projectId ? { projectId: projectId as any } : "skip"
  );

  // Fetch log entries for this project
  const logEntries = useQuery(
    api.logEntries.getByProject,
    projectId && user?.id && project ? {
      projectId: projectId as any,
      userId: user.id
    } : "skip"
  );



















  // Modern loading state with skeletons
  if (project === undefined || logEntries === undefined) {
    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          {/* Header Skeleton */}
          <div className="flex items-center gap-4 mb-8">
            <div className="skeleton h-10 w-10 rounded-full"></div>
            <div className="skeleton h-8 w-64"></div>
            <div className="ml-auto skeleton h-10 w-32 rounded-lg"></div>
          </div>

          {/* Project Info Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="bg-base-100 rounded-xl p-8 shadow-lg">
                <div className="skeleton h-8 w-48 mb-4"></div>
                <div className="skeleton h-20 w-full mb-6"></div>
                <div className="flex gap-4">
                  <div className="skeleton h-12 w-32 rounded-lg"></div>
                  <div className="skeleton h-12 w-32 rounded-lg"></div>
                </div>
              </div>
            </div>
            <div>
              <div className="bg-base-100 rounded-xl p-6 shadow-lg">
                <div className="skeleton h-6 w-24 mb-4"></div>
                <div className="space-y-3">
                  <div className="skeleton h-4 w-full"></div>
                  <div className="skeleton h-4 w-3/4"></div>
                  <div className="skeleton h-4 w-1/2"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Gallery Skeleton */}
          <div className="bg-white rounded-xl p-8 shadow-lg border border-jobblogg-border">
            <div className="skeleton h-8 w-32 mb-6"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="skeleton h-48 w-full rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Modern error state for unauthorized/not found
  if (!project || project.userId !== user?.id) {
    return (
      <div className="min-h-screen bg-base-200/30 animate-fade-in">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="flex items-center gap-4 mb-8">
            <Link
              to="/"
              className="btn btn-ghost btn-circle btn-modern hover:bg-jobblogg-primary/10"
              aria-label="Tilbake til oversikt"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
          </div>

          <div className="bg-jobblogg-error-soft border border-jobblogg-error/20 rounded-xl p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-error/20 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <Heading2 className="text-jobblogg-error mb-2">Prosjekt ikke funnet</Heading2>
            <BodyText className="text-jobblogg-error/80 mb-6">
              Dette prosjektet eksisterer ikke eller du har ikke tilgang til det.
            </BodyText>
            <PrimaryButton onClick={() => navigate('/')}>
              Tilbake til oversikt
            </PrimaryButton>
          </div>
        </div>
      </div>
    );
  }

  // Sort log entries by creation date (newest first)
  const sortedLogEntries = logEntries.sort((a, b) => b.createdAt - a.createdAt);

  // Filter for user-created entries only (excludes system activity entries like archive/restore)
  const userLogEntries = sortedLogEntries.filter(entry =>
    entry.entryType !== "system" // Include entries that are explicitly "user" or have no entryType (backward compatibility)
  );

  // Filter for entries with actual images for the image gallery
  const imageEntries = userLogEntries.filter(entry => entry.imageUrl);

  return (
    <PageLayout
      title={project.name}
      showBackButton
      backUrl="/"
      containerWidth="wide"
      headerActions={
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
          <ArchiveStatusBadge
            isArchived={project.isArchived}
            archivedAt={project.archivedAt}
          />
          <div className="flex items-center gap-2 px-3 py-1.5 bg-jobblogg-primary-soft text-jobblogg-primary rounded-full text-sm font-medium">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="hidden sm:inline">Kontraktørvisning</span>
            <span className="sm:hidden">Kontraktør</span>
          </div>
          <TextMuted className="text-sm sm:text-lg hidden md:block">
            🔧 Intern arbeidsdetaljer og dokumentasjon
          </TextMuted>
          <div className="flex flex-wrap gap-2 w-full sm:w-auto">
            <PrimaryButton
              onClick={() => setShowShareModal(true)}
              variant="secondary"
              className="flex items-center gap-2 min-h-[44px] flex-1 sm:flex-none"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              <span className="text-sm sm:text-base">Del prosjekt</span>
            </PrimaryButton>
            <PrimaryButton
              onClick={() => navigate(`/project/${projectId}`)}
              className="flex items-center gap-2 min-h-[44px] flex-1 sm:flex-none"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              <span className="text-sm sm:text-base">Legg til bilde</span>
            </PrimaryButton>
          </div>
        </div>
      }
    >
      <div className="w-full px-4 sm:px-0 sm:max-w-4xl sm:mx-auto space-y-6 sm:space-y-8">

        {/* Project Information */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Prosjektinformasjon</Heading2>

          <div>
            <Heading3>Prosjektsammendrag</Heading3>
            <div className="bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border">
              <BodyText className="leading-relaxed">
                {project.description || (
                  <TextMuted className="italic">
                    Ingen sammendrag tilgjengelig. Du kan legge til et sammendrag ved å redigere prosjektet.
                  </TextMuted>
                )}
              </BodyText>
            </div>
          </div>

          {/* Customer Information */}
          {project.customer && (
            <div>
              <Heading3>Kundeinformasjon</Heading3>
              <div className="bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Customer Name */}
                        <div>
                          <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                            {project.customer.type === 'firma' ? 'Firmanavn' : 'Kundenavn'}
                          </TextMuted>
                          <BodyText className="font-medium">
                            {project.customer.name}
                          </BodyText>
                          {project.customer.type === 'firma' && project.customer.contactPerson && (
                            <TextMuted className="text-sm mt-1">
                              Kontaktperson: {project.customer.contactPerson}
                            </TextMuted>
                          )}
                        </div>

                        {/* Customer Address */}
                        <div>
                          <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                            Adresse
                          </TextMuted>
                          <BodyText>
                            {project.customer.address}
                          </BodyText>
                        </div>

                        {/* Phone Number */}
                        {project.customer.phone && (
                          <div>
                            <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                              Telefon
                            </TextMuted>
                            <BodyText>
                              <a
                                href={`tel:${project.customer.phone}`}
                                className="text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors duration-200 hover:underline"
                              >
                                {project.customer.phone}
                              </a>
                            </BodyText>
                          </div>
                        )}

                        {/* Email */}
                        {project.customer.email && (
                          <div>
                            <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                              E-post
                            </TextMuted>
                            <BodyText>
                              <a
                                href={`mailto:${project.customer.email}`}
                                className="text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors duration-200 hover:underline"
                              >
                                {project.customer.email}
                              </a>
                            </BodyText>
                          </div>
                        )}

                        {/* Organization Number (for companies) */}
                        {project.customer.type === 'firma' && project.customer.orgNumber && (
                          <div>
                            <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                              Organisasjonsnummer
                            </TextMuted>
                            <BodyText>
                              {project.customer.orgNumber}
                            </BodyText>
                          </div>
                        )}

                        {/* Customer Notes */}
                        {project.customer.notes && (
                          <div className="md:col-span-2">
                            <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                              Notater
                            </TextMuted>
                            <BodyText className="text-sm">
                              {project.customer.notes}
                            </BodyText>
                          </div>
                        )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Job Information */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Jobbinformasjon</Heading2>

          <div>
            <Heading3>Jobbdetaljer</Heading3>
            <div className="bg-jobblogg-neutral rounded-lg p-4 border border-jobblogg-border">
              <BodyText className="leading-relaxed">
                {project.description || (
                  <TextMuted className="italic">
                    Ingen jobbdetaljer tilgjengelig. Du kan legge til jobbdetaljer ved å redigere prosjektet.
                  </TextMuted>
                )}
              </BodyText>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-jobblogg-border">
            <PrimaryButton
              onClick={() => navigate(`/project/${projectId}`)}
              className="min-h-[44px] w-full sm:w-auto flex-1 sm:flex-none"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              <span className="text-sm sm:text-base">Legg til bilde</span>
            </PrimaryButton>
            <ArchiveActions
              projectId={projectId!}
              isArchived={project?.isArchived}
              onArchiveComplete={() => navigate('/', { replace: true })}
              onRestoreComplete={() => navigate('/', { replace: true })}
              className="min-h-[44px] w-full sm:w-auto flex-1 sm:flex-none"
            />
          </div>
        </div>

        {/* Statistics */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Statistikk</Heading2>

          <div className="grid grid-cols-2 gap-4">
            <div className="bg-jobblogg-primary-soft rounded-lg p-3 sm:p-4 text-center">
              <div className="text-2xl sm:text-3xl font-bold text-jobblogg-primary mb-1">
                {imageEntries.length}
              </div>
              <TextMuted className="text-xs sm:text-sm">Totalt bilder</TextMuted>
            </div>

            <div className="bg-jobblogg-accent-soft rounded-lg p-3 sm:p-4 text-center">
              <div className="text-base sm:text-lg font-bold text-jobblogg-accent mb-1">
                {userLogEntries.length > 0
                  ? formatShortDate(userLogEntries[0].createdAt)
                  : 'Ingen aktivitet'
                }
              </div>
              <TextMuted className="text-xs sm:text-sm">Siste aktivitet</TextMuted>
            </div>
        </div>

        {/* Share Modal */}
        {showShareModal && (
          <ShareProjectModal
            isOpen={showShareModal}
            onClose={() => setShowShareModal(false)}
            project={project}
            userId={user?.id || ''}
          />
        )}
      </div>
    </div>
    </PageLayout>
  );
};

export default ProjectDetail;

